"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateParams = exports.validateQuery = exports.validateBody = void 0;
const validateBody = (schema) => {
    return (req, res, next) => {
        const errors = validateObject(req.body, schema);
        if (errors.length > 0) {
            res.status(422).json({
                success: false,
                message: 'Validation failed',
                error: 'Request body validation errors',
                statusCode: 422,
                timestamp: new Date().toISOString(),
                validationErrors: errors
            });
            return;
        }
        next();
    };
};
exports.validateBody = validateBody;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const errors = validateObject(req.query, schema);
        if (errors.length > 0) {
            res.status(422).json({
                success: false,
                message: 'Validation failed',
                error: 'Query parameter validation errors',
                statusCode: 422,
                timestamp: new Date().toISOString(),
                validationErrors: errors
            });
            return;
        }
        next();
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        const errors = validateObject(req.params, schema);
        if (errors.length > 0) {
            res.status(422).json({
                success: false,
                message: 'Validation failed',
                error: 'URL parameter validation errors',
                statusCode: 422,
                timestamp: new Date().toISOString(),
                validationErrors: errors
            });
            return;
        }
        next();
    };
};
exports.validateParams = validateParams;
function validateObject(obj, schema) {
    const errors = [];
    for (const [field, rules] of Object.entries(schema)) {
        const value = obj[field];
        if (rules.required && (value === undefined || value === null || value === '')) {
            errors.push({
                field,
                message: `${field} is required`,
                value
            });
            continue;
        }
        if (value === undefined || value === null) {
            continue;
        }
        if (rules.type && !validateType(value, rules.type)) {
            errors.push({
                field,
                message: `${field} must be of type ${rules.type}`,
                value
            });
            continue;
        }
        if (rules.type === 'string' && typeof value === 'string') {
            if (rules.minLength && value.length < rules.minLength) {
                errors.push({
                    field,
                    message: `${field} must be at least ${rules.minLength} characters long`,
                    value
                });
            }
            if (rules.maxLength && value.length > rules.maxLength) {
                errors.push({
                    field,
                    message: `${field} must be no more than ${rules.maxLength} characters long`,
                    value
                });
            }
            if (rules.pattern && !rules.pattern.test(value)) {
                errors.push({
                    field,
                    message: `${field} format is invalid`,
                    value
                });
            }
        }
        if (rules.type === 'number' && typeof value === 'number') {
            if (rules.min !== undefined && value < rules.min) {
                errors.push({
                    field,
                    message: `${field} must be at least ${rules.min}`,
                    value
                });
            }
            if (rules.max !== undefined && value > rules.max) {
                errors.push({
                    field,
                    message: `${field} must be no more than ${rules.max}`,
                    value
                });
            }
        }
        if (rules.enum && !rules.enum.includes(value)) {
            errors.push({
                field,
                message: `${field} must be one of: ${rules.enum.join(', ')}`,
                value
            });
        }
        if (rules.custom) {
            const result = rules.custom(value);
            if (result !== true) {
                errors.push({
                    field,
                    message: typeof result === 'string' ? result : `${field} is invalid`,
                    value
                });
            }
        }
    }
    return errors;
}
function validateType(value, type) {
    switch (type) {
        case 'string':
            return typeof value === 'string';
        case 'number':
            return typeof value === 'number' && !isNaN(value);
        case 'boolean':
            return typeof value === 'boolean';
        case 'object':
            return typeof value === 'object' && value !== null && !Array.isArray(value);
        case 'array':
            return Array.isArray(value);
        default:
            return true;
    }
}
//# sourceMappingURL=validation.js.map