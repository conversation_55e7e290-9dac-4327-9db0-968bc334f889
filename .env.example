# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# API Configuration
API_PREFIX=/api
API_VERSION=v1

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Database Configuration (for future use)
DATABASE_URL=
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cravin_concierge
DB_USERNAME=
DB_PASSWORD=



# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Application Configuration
APP_NAME=Cravin Concierge API
APP_VERSION=1.0.0
APP_DESCRIPTION=TypeScript Express.js MVC API
