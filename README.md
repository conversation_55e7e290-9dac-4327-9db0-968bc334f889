# Cravin Concierge - TypeScript Express.js MVC API

A robust TypeScript-based Express.js backend application following the Model-View-Controller (MVC) architecture pattern.

## 🚀 Features

- **TypeScript**: Full type safety and enhanced developer experience
- **Express.js**: Fast, unopinionated web framework for Node.js
- **MVC Architecture**: Clean separation of concerns with Models, Views, and Controllers
- **Security**: Comprehensive security middleware (Helmet, CORS, Rate limiting)
- **Error Handling**: Centralized error handling with custom error classes
- **Logging**: Request/response logging with Morgan
- **Validation**: Request validation middleware
- **Hot Reloading**: Development environment with automatic restart
- **Path Mapping**: TypeScript path aliases for clean imports

## 📁 Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Request handlers (Controller layer)
├── middleware/      # Custom middleware functions
├── models/          # Data models (Model layer)
├── routes/          # Route definitions
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
├── views/           # Response formatting (View layer)
├── app.ts           # Express application setup
└── server.ts        # Application entry point
```

## 🛠️ Installation

1. Clone the repository:
```bash
git clone https://github.com/Just-Cravin/Cravin-Concierge.git
cd Cravin-Concierge
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm run build
npm start
```

### Other Scripts
```bash
npm run build        # Build TypeScript to JavaScript
npm run clean        # Clean build directory
npm run type-check   # Type check without building
```

## 📚 API Endpoints

### Health Check
- `GET /api/health` - API health status

### Users
- `GET /api/users` - Get all users (with pagination)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `PATCH /api/users/:id/activate` - Activate user
- `PATCH /api/users/:id/deactivate` - Deactivate user

### Products
- `GET /api/products` - Get all products (with pagination)
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product
- `PATCH /api/products/:id/stock` - Update product stock
- `GET /api/products/category/:category` - Get products by category

## 🔧 Configuration

The application uses environment variables for configuration. See `.env.example` for available options:

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development/production)
- `API_PREFIX`: API route prefix (default: /api)
- `CORS_ORIGINS`: Allowed CORS origins
- `RATE_LIMIT_MAX`: Rate limit per window


## 🏗️ Architecture

### MVC Pattern
- **Models**: Data structures and business logic (`src/models/`)
- **Views**: Response formatting and data presentation (`src/controllers/`)
- **Controllers**: Request handling and coordination (`src/controllers/`)

### Middleware Stack
1. Security headers (Helmet)
2. CORS handling
3. Rate limiting
4. Request parsing
5. Logging
6. Route handling
7. Error handling

## 🔒 Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Request rate limiting
- **Input Validation**: Request validation middleware
- **Error Handling**: Secure error responses

## 🧪 Testing

```bash
npm test  # Run tests (to be implemented)
```

## 📝 Development

### Adding New Routes
1. Create controller in `src/controllers/`
2. Define routes in `src/routes/`
3. Add route to main router in `src/routes/index.ts`

### Adding New Models
1. Create model class extending `BaseModel`
2. Implement required methods (`validate`, `getSerializableData`)
3. Add TypeScript interfaces in the same file

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.
