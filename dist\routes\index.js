"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.routes = exports.Routes = void 0;
const express_1 = require("express");
const userRoutes_1 = require("./userRoutes");
const productRoutes_1 = require("./productRoutes");
class Routes {
    constructor() {
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/health', (req, res) => {
            res.status(200).json({
                success: true,
                message: 'API is healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || 'development'
            });
        });
        this.router.get('/', (req, res) => {
            res.status(200).json({
                success: true,
                message: 'Welcome to Cravin Concierge API',
                version: '1.0.0',
                timestamp: new Date().toISOString(),
                endpoints: {
                    users: '/api/users',
                    products: '/api/products',
                    health: '/api/health'
                }
            });
        });
        this.router.use('/users', userRoutes_1.userRoutes);
        this.router.use('/products', productRoutes_1.productRoutes);
    }
}
exports.Routes = Routes;
exports.routes = new Routes().router;
//# sourceMappingURL=index.js.map