import { Request, Response } from 'express';
import { HttpStatusCode } from '../types/common';
export declare abstract class BaseController {
    protected sendSuccess<T>(res: Response, data: T, message?: string, statusCode?: HttpStatusCode): void;
    protected sendPaginatedResponse<T>(res: Response, data: T[], page: number, limit: number, total: number, message?: string): void;
    protected sendError(res: Response, message: string, statusCode?: HttpStatusCode, error?: string, validationErrors?: any[]): void;
    protected sendNotFound(res: Response, resource?: string): void;
    protected sendBadRequest(res: Response, message?: string): void;
    protected sendValidationError(res: Response, errors: any[]): void;
    protected sendUnauthorized(res: Response, message?: string): void;
    protected sendForbidden(res: Response, message?: string): void;
    protected getPaginationParams(req: Request): {
        page: number;
        limit: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    };
    protected asyncHandler: (fn: (req: Request, res: Response) => Promise<void>) => (req: Request, res: Response) => void;
}
//# sourceMappingURL=BaseController.d.ts.map