import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Environment configuration
 */
export const config = {
  // Server configuration
  port: parseInt(process.env['PORT'] || '3000', 10),
  host: process.env['HOST'] || 'localhost',
  nodeEnv: process.env['NODE_ENV'] || 'development',

  // API configuration
  apiPrefix: process.env['API_PREFIX'] || '/api',
  apiVersion: process.env['API_VERSION'] || 'v1',

  // Security configuration
  corsOrigins: process.env['CORS_ORIGINS']?.split(',') || ['http://localhost:3000'],
  rateLimitWindow: parseInt(process.env['RATE_LIMIT_WINDOW'] || '900000', 10), // 15 minutes
  rateLimitMax: parseInt(process.env['RATE_LIMIT_MAX'] || '100', 10),

  // Database configuration (for future use)
  database: {
    url: process.env['DATABASE_URL'] || '',
    host: process.env['DB_HOST'] || 'localhost',
    port: parseInt(process.env['DB_PORT'] || '5432', 10),
    name: process.env['DB_NAME'] || 'cravin_concierge',
    username: process.env['DB_USERNAME'] || '',
    password: process.env['DB_PASSWORD'] || '',
  },

  // JWT configuration (for future use)
  jwt: {
    secret: process.env['JWT_SECRET'] || 'your-super-secret-jwt-key',
    expiresIn: process.env['JWT_EXPIRES_IN'] || '24h',
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d',
  },

  // Logging configuration
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    format: process.env['LOG_FORMAT'] || 'combined',
  },

  // Application configuration
  app: {
    name: process.env['APP_NAME'] || 'Cravin Concierge API',
    version: process.env['APP_VERSION'] || '1.0.0',
    description: process.env['APP_DESCRIPTION'] || 'TypeScript Express.js MVC API',
  }
};

/**
 * Validate required environment variables
 */
export const validateConfig = (): void => {
  const requiredVars: string[] = [
    // Add required environment variables here
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
};

/**
 * Check if running in development mode
 */
export const isDevelopment = (): boolean => {
  return config.nodeEnv === 'development';
};

/**
 * Check if running in production mode
 */
export const isProduction = (): boolean => {
  return config.nodeEnv === 'production';
};

/**
 * Check if running in test mode
 */
export const isTest = (): boolean => {
  return config.nodeEnv === 'test';
};
