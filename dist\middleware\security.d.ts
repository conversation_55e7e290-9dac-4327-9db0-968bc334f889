import { Request, Response, NextFunction } from 'express';
export declare const apiLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const corsOptions: {
    origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void;
    methods: string[];
    allowedHeaders: string[];
    exposedHeaders: string[];
    credentials: boolean;
    maxAge: number;
};
export declare const securityHeaders: (req: Request, res: Response, next: NextFunction) => void;
export declare const contentSecurityPolicy: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=security.d.ts.map