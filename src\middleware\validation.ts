import { Request, Response, NextFunction } from 'express';
import { ValidationError } from '../types/common';

/**
 * Validation schema interface
 */
export interface ValidationSchema {
  [key: string]: {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array';
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: any[];
    custom?: (value: any) => boolean | string;
  };
}

/**
 * Validate request body against schema
 */
export const validateBody = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors = validateObject(req.body, schema);
    
    if (errors.length > 0) {
      res.status(422).json({
        success: false,
        message: 'Validation failed',
        error: 'Request body validation errors',
        statusCode: 422,
        timestamp: new Date().toISOString(),
        validationErrors: errors
      });
      return;
    }
    
    next();
  };
};

/**
 * Validate request query parameters against schema
 */
export const validateQuery = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors = validateObject(req.query, schema);
    
    if (errors.length > 0) {
      res.status(422).json({
        success: false,
        message: 'Validation failed',
        error: 'Query parameter validation errors',
        statusCode: 422,
        timestamp: new Date().toISOString(),
        validationErrors: errors
      });
      return;
    }
    
    next();
  };
};

/**
 * Validate request parameters against schema
 */
export const validateParams = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors = validateObject(req.params, schema);
    
    if (errors.length > 0) {
      res.status(422).json({
        success: false,
        message: 'Validation failed',
        error: 'URL parameter validation errors',
        statusCode: 422,
        timestamp: new Date().toISOString(),
        validationErrors: errors
      });
      return;
    }
    
    next();
  };
};

/**
 * Validate object against schema
 */
function validateObject(obj: any, schema: ValidationSchema): ValidationError[] {
  const errors: ValidationError[] = [];
  
  // Check required fields
  for (const [field, rules] of Object.entries(schema)) {
    const value = obj[field];
    
    // Check if required field is missing
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field,
        message: `${field} is required`,
        value
      });
      continue;
    }
    
    // Skip validation if field is not provided and not required
    if (value === undefined || value === null) {
      continue;
    }
    
    // Type validation
    if (rules.type && !validateType(value, rules.type)) {
      errors.push({
        field,
        message: `${field} must be of type ${rules.type}`,
        value
      });
      continue;
    }
    
    // String validations
    if (rules.type === 'string' && typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        errors.push({
          field,
          message: `${field} must be at least ${rules.minLength} characters long`,
          value
        });
      }
      
      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push({
          field,
          message: `${field} must be no more than ${rules.maxLength} characters long`,
          value
        });
      }
      
      if (rules.pattern && !rules.pattern.test(value)) {
        errors.push({
          field,
          message: `${field} format is invalid`,
          value
        });
      }
    }
    
    // Number validations
    if (rules.type === 'number' && typeof value === 'number') {
      if (rules.min !== undefined && value < rules.min) {
        errors.push({
          field,
          message: `${field} must be at least ${rules.min}`,
          value
        });
      }
      
      if (rules.max !== undefined && value > rules.max) {
        errors.push({
          field,
          message: `${field} must be no more than ${rules.max}`,
          value
        });
      }
    }
    
    // Enum validation
    if (rules.enum && !rules.enum.includes(value)) {
      errors.push({
        field,
        message: `${field} must be one of: ${rules.enum.join(', ')}`,
        value
      });
    }
    
    // Custom validation
    if (rules.custom) {
      const result = rules.custom(value);
      if (result !== true) {
        errors.push({
          field,
          message: typeof result === 'string' ? result : `${field} is invalid`,
          value
        });
      }
    }
  }
  
  return errors;
}

/**
 * Validate value type
 */
function validateType(value: any, type: string): boolean {
  switch (type) {
    case 'string':
      return typeof value === 'string';
    case 'number':
      return typeof value === 'number' && !isNaN(value);
    case 'boolean':
      return typeof value === 'boolean';
    case 'object':
      return typeof value === 'object' && value !== null && !Array.isArray(value);
    case 'array':
      return Array.isArray(value);
    default:
      return true;
  }
}
