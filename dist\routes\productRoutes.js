"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.productRoutes = exports.ProductRoutes = void 0;
const express_1 = require("express");
const ProductController_1 = require("@controllers/ProductController");
class ProductRoutes {
    constructor() {
        this.router = (0, express_1.Router)();
        this.productController = new ProductController_1.ProductController();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', this.productController.getAllProducts);
        this.router.get('/:id', this.productController.getProductById);
        this.router.post('/', this.productController.createProduct);
        this.router.put('/:id', this.productController.updateProduct);
        this.router.delete('/:id', this.productController.deleteProduct);
        this.router.patch('/:id/stock', this.productController.updateStock);
        this.router.get('/category/:category', this.productController.getProductsByCategory);
    }
}
exports.ProductRoutes = ProductRoutes;
exports.productRoutes = new ProductRoutes().router;
//# sourceMappingURL=productRoutes.js.map