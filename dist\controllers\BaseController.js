"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
class BaseController {
    constructor() {
        this.asyncHandler = (fn) => {
            return (req, res) => {
                Promise.resolve(fn(req, res)).catch((error) => {
                    console.error('Controller error:', error);
                    this.sendError(res, 'Internal server error', 500, error.message);
                });
            };
        };
    }
    sendSuccess(res, data, message = 'Success', statusCode = 200) {
        const response = {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString()
        };
        res.status(statusCode).json(response);
    }
    sendPaginatedResponse(res, data, page, limit, total, message = 'Success') {
        const totalPages = Math.ceil(total / limit);
        const response = {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString(),
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        };
        res.status(200).json(response);
    }
    sendError(res, message, statusCode = 500, error, validationErrors) {
        const response = {
            success: false,
            message,
            error: error || message,
            statusCode,
            timestamp: new Date().toISOString(),
            ...(validationErrors && { validationErrors })
        };
        res.status(statusCode).json(response);
    }
    sendNotFound(res, resource = 'Resource') {
        this.sendError(res, `${resource} not found`, 404);
    }
    sendBadRequest(res, message = 'Bad request') {
        this.sendError(res, message, 400);
    }
    sendValidationError(res, errors) {
        this.sendError(res, 'Validation failed', 422, 'Validation errors occurred', errors);
    }
    sendUnauthorized(res, message = 'Unauthorized') {
        this.sendError(res, message, 401);
    }
    sendForbidden(res, message = 'Forbidden') {
        this.sendError(res, message, 403);
    }
    getPaginationParams(req) {
        const page = Math.max(1, parseInt(req.query['page']) || 1);
        const limit = Math.min(100, Math.max(1, parseInt(req.query['limit']) || 10));
        const sortBy = req.query['sortBy'];
        const sortOrder = req.query['sortOrder'] === 'desc' ? 'desc' : 'asc';
        return { page, limit, sortBy, sortOrder };
    }
}
exports.BaseController = BaseController;
//# sourceMappingURL=BaseController.js.map