"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const environment_1 = require("./config/environment");
const index_1 = require("./routes/index");
const errorHandler_1 = require("./middleware/errorHandler");
const logger_1 = require("./middleware/logger");
const security_1 = require("./middleware/security");
class App {
    constructor() {
        this.app = (0, express_1.default)();
        this.port = environment_1.config.port;
        this.validateEnvironment();
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    validateEnvironment() {
        try {
            (0, environment_1.validateConfig)();
            console.log('✅ Environment configuration validated');
        }
        catch (error) {
            console.error('❌ Environment validation failed:', error);
            process.exit(1);
        }
    }
    initializeMiddleware() {
        this.app.set('trust proxy', 1);
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: false
        }));
        this.app.use(security_1.securityHeaders);
        this.app.use(security_1.contentSecurityPolicy);
        this.app.use((0, cors_1.default)(security_1.corsOptions));
        this.app.use(security_1.apiLimiter);
        this.app.use((0, compression_1.default)());
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.use(logger_1.requestId);
        this.app.use((0, logger_1.getLogger)());
        console.log('✅ Middleware initialized');
    }
    initializeRoutes() {
        this.app.use(environment_1.config.apiPrefix, index_1.routes);
        this.app.get('/', (req, res) => {
            res.status(200).json({
                success: true,
                message: `Welcome to ${environment_1.config.app.name}`,
                version: environment_1.config.app.version,
                description: environment_1.config.app.description,
                timestamp: new Date().toISOString(),
                environment: environment_1.config.nodeEnv,
                apiEndpoint: `${req.protocol}://${req.get('host')}${environment_1.config.apiPrefix}`
            });
        });
        console.log('✅ Routes initialized');
    }
    initializeErrorHandling() {
        this.app.use(errorHandler_1.notFoundHandler);
        this.app.use(errorHandler_1.errorHandler);
        console.log('✅ Error handling initialized');
    }
    listen() {
        this.app.listen(this.port, environment_1.config.host, () => {
            console.log('\n🚀 Server started successfully!');
            console.log(`📍 Server running at: http://${environment_1.config.host}:${this.port}`);
            console.log(`🌐 API endpoint: http://${environment_1.config.host}:${this.port}${environment_1.config.apiPrefix}`);
            console.log(`🔧 Environment: ${environment_1.config.nodeEnv}`);
            console.log(`📊 Process ID: ${process.pid}`);
            console.log(`⏰ Started at: ${new Date().toISOString()}\n`);
        });
        this.setupGracefulShutdown();
    }
    setupGracefulShutdown() {
        const gracefulShutdown = (signal) => {
            console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
            const server = this.app.listen();
            server.close(() => {
                console.log('✅ HTTP server closed');
                console.log('✅ Graceful shutdown completed');
                process.exit(0);
            });
            setTimeout(() => {
                console.error('❌ Could not close connections in time, forcefully shutting down');
                process.exit(1);
            }, 10000);
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            console.error('❌ Uncaught Exception:', error);
            process.exit(1);
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
            process.exit(1);
        });
    }
    getApp() {
        return this.app;
    }
}
exports.App = App;
//# sourceMappingURL=app.js.map