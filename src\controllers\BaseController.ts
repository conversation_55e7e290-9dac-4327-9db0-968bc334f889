import { Request, Response } from 'express';
import { ApiResponse, ErrorResponse, HttpStatusCode, PaginatedResponse } from '../types/common';

/**
 * Base controller class providing common functionality for all controllers
 */
export abstract class BaseController {
  /**
   * Send a successful response
   */
  protected sendSuccess<T>(
    res: Response,
    data: T,
    message: string = 'Success',
    statusCode: HttpStatusCode = 200
  ): void {
    const response: ApiResponse<T> = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send a paginated response
   */
  protected sendPaginatedResponse<T>(
    res: Response,
    data: T[],
    page: number,
    limit: number,
    total: number,
    message: string = 'Success'
  ): void {
    const totalPages = Math.ceil(total / limit);
    
    const response: PaginatedResponse<T> = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    res.status(200).json(response);
  }

  /**
   * Send an error response
   */
  protected sendError(
    res: Response,
    message: string,
    statusCode: HttpStatusCode = 500,
    error?: string,
    validationErrors?: any[]
  ): void {
    const response: ErrorResponse = {
      success: false,
      message,
      error: error || message,
      statusCode,
      timestamp: new Date().toISOString(),
      ...(validationErrors && { validationErrors })
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send a not found response
   */
  protected sendNotFound(res: Response, resource: string = 'Resource'): void {
    this.sendError(res, `${resource} not found`, 404);
  }

  /**
   * Send a bad request response
   */
  protected sendBadRequest(res: Response, message: string = 'Bad request'): void {
    this.sendError(res, message, 400);
  }

  /**
   * Send a validation error response
   */
  protected sendValidationError(res: Response, errors: any[]): void {
    this.sendError(res, 'Validation failed', 422, 'Validation errors occurred', errors);
  }

  /**
   * Send an unauthorized response
   */
  protected sendUnauthorized(res: Response, message: string = 'Unauthorized'): void {
    this.sendError(res, message, 401);
  }

  /**
   * Send a forbidden response
   */
  protected sendForbidden(res: Response, message: string = 'Forbidden'): void {
    this.sendError(res, message, 403);
  }

  /**
   * Extract pagination parameters from request
   */
  protected getPaginationParams(req: Request): { page: number; limit: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } {
    const page = Math.max(1, parseInt(req.query['page'] as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query['limit'] as string) || 10));
    const sortBy = req.query['sortBy'] as string;
    const sortOrder = (req.query['sortOrder'] as string) === 'desc' ? 'desc' : 'asc';

    return { page, limit, sortBy, sortOrder };
  }

  /**
   * Handle async controller methods and catch errors
   */
  protected asyncHandler = (fn: (req: Request, res: Response) => Promise<void>) => {
    return (req: Request, res: Response): void => {
      Promise.resolve(fn(req, res)).catch((error) => {
        console.error('Controller error:', error);
        this.sendError(res, 'Internal server error', 500, error.message);
      });
    };
  };
}
