"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = exports.responseTime = exports.requestId = exports.getLogger = void 0;
const morgan_1 = __importDefault(require("morgan"));
morgan_1.default.token('response-time-ms', (_req, res) => {
    const responseTime = res.getHeader('X-Response-Time');
    return responseTime ? `${responseTime}ms` : '-';
});
morgan_1.default.token('request-id', (req) => {
    return req.requestId || '-';
});
morgan_1.default.token('user', (req) => {
    return req.user?.id || 'anonymous';
});
const developmentFormat = ':method :url :status :res[content-length] - :response-time ms';
const productionFormat = ':remote-addr - :user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time ms';
const getLogger = () => {
    const format = process.env['NODE_ENV'] === 'production' ? productionFormat : developmentFormat;
    return (0, morgan_1.default)(format, {
        skip: (req, _res) => {
            if (process.env['NODE_ENV'] === 'production' && req.url === '/api/health') {
                return true;
            }
            return false;
        },
        stream: {
            write: (message) => {
                console.log(message.trim());
            }
        }
    });
};
exports.getLogger = getLogger;
const requestId = (req, res, next) => {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    req.requestId = id;
    res.setHeader('X-Request-ID', id);
    next();
};
exports.requestId = requestId;
const responseTime = (_req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        res.setHeader('X-Response-Time', duration);
    });
    next();
};
exports.responseTime = responseTime;
const requestLogger = (req, res, next) => {
    const start = Date.now();
    if (process.env['NODE_ENV'] === 'development') {
        console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`, {
            headers: req.headers,
            query: req.query,
            body: req.body,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
    }
    res.on('finish', () => {
        const duration = Date.now() - start;
        if (process.env['NODE_ENV'] === 'development') {
            console.log(`[${new Date().toISOString()}] Response ${res.statusCode} - ${duration}ms`);
        }
    });
    next();
};
exports.requestLogger = requestLogger;
//# sourceMappingURL=logger.js.map