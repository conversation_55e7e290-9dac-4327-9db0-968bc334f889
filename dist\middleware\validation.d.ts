import { Request, Response, NextFunction } from 'express';
export interface ValidationSchema {
    [key: string]: {
        required?: boolean;
        type?: 'string' | 'number' | 'boolean' | 'object' | 'array';
        minLength?: number;
        maxLength?: number;
        min?: number;
        max?: number;
        pattern?: RegExp;
        enum?: any[];
        custom?: (value: any) => boolean | string;
    };
}
export declare const validateBody: (schema: ValidationSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateQuery: (schema: ValidationSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateParams: (schema: ValidationSchema) => (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.d.ts.map