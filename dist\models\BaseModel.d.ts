import { BaseEntity } from '../types/common';
export declare abstract class BaseModel implements BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    constructor(data?: Partial<BaseEntity>);
    private generateId;
    touch(): void;
    toJSON(): Record<string, any>;
    protected abstract getSerializableData(): Record<string, any>;
    abstract validate(): {
        isValid: boolean;
        errors: string[];
    };
    static fromJSON<T extends BaseModel>(this: new (data: any) => T, data: Record<string, any>): T;
}
//# sourceMappingURL=BaseModel.d.ts.map