import { BaseEntity } from '../types/common';

/**
 * Base model class that provides common functionality for all models
 */
export abstract class BaseModel implements BaseEntity {
  public id: string;
  public createdAt: Date;
  public updatedAt: Date;

  constructor(data: Partial<BaseEntity> = {}) {
    this.id = data.id || this.generateId();
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * Generate a unique ID for the entity
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update the updatedAt timestamp
   */
  public touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * Convert the model to a plain object
   */
  public toJSON(): Record<string, any> {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      ...this.getSerializableData()
    };
  }

  /**
   * Get serializable data specific to the model
   * Override this method in child classes
   */
  protected abstract getSerializableData(): Record<string, any>;

  /**
   * Validate the model data
   * Override this method in child classes
   */
  public abstract validate(): { isValid: boolean; errors: string[] };

  /**
   * Create a new instance from plain object data
   */
  public static fromJSON<T extends BaseModel>(
    this: new (data: any) => T,
    data: Record<string, any>
  ): T {
    return new this(data);
  }
}
