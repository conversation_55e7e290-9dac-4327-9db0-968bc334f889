export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    timestamp: string;
}
export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export type HttpStatusCode = 200 | 201 | 204 | 400 | 401 | 403 | 404 | 409 | 422 | 500;
export interface ErrorResponse {
    success: false;
    message: string;
    error: string;
    statusCode: HttpStatusCode;
    timestamp: string;
    path?: string;
    validationErrors?: ValidationError[];
}
//# sourceMappingURL=common.d.ts.map